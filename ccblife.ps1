# 简化的CcbLife部署脚本

Write-Host "=== CcbLife Deployment Start ===" -ForegroundColor Green

# Check Docker
Write-Host "Checking Docker environment..." -ForegroundColor Yellow
$dockerVersion = docker --version
$composeVersion = docker-compose --version
Write-Host "Docker environment OK" -ForegroundColor Green

# Create directories
Write-Host "Creating directories..." -ForegroundColor Yellow
if (!(Test-Path "logs")) {
    New-Item -ItemType Directory -Path "logs" -Force | Out-Null
}
if (!(Test-Path "logs/nginx")) {
    New-Item -ItemType Directory -Path "logs/nginx" -Force | Out-Null
}
Write-Host "Directories created" -ForegroundColor Green

# Stop existing containers
Write-Host "Stopping existing containers..." -ForegroundColor Yellow
docker-compose down --remove-orphans
Write-Host "Containers stopped" -ForegroundColor Green

# Build and start
Write-Host "Building and starting application..." -ForegroundColor Yellow
docker-compose up -d --build

Write-Host "=== Deployment Complete ===" -ForegroundColor Green
Write-Host "Access URL: http://localhost:8092" -ForegroundColor Cyan 