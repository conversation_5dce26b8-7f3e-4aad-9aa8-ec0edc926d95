# 依赖目录
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
dist
build

# 开发环境文件
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs
*.log

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage
.nyc_output

# 依赖锁定文件（可选）
# package-lock.json
# yarn.lock

# IDE 和编辑器
.vscode
.idea
*.swp
*.swo
*~

# 操作系统
.DS_Store
Thumbs.db

# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# 文档
README.md
*.md

# 测试文件
test
tests
__tests__
*.test.js
*.spec.js

# 临时文件
.tmp
.temp 