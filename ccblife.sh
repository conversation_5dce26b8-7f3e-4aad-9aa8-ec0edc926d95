#!/bin/bash

# CcbLife 前端项目部署脚本 (Linux/macOS)

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    log_info "Docker 环境检查通过"
}

# 创建目录
create_dirs() {
    mkdir -p logs/nginx
    log_info "目录创建完成"
}

# 构建和部署
deploy() {
    log_info "开始部署..."
    
    # 停止现有容器
    docker-compose down --remove-orphans
    
    # 构建并启动
    docker-compose up -d --build
    
    log_info "部署完成，访问 http://localhost:8092"
}

# 查看日志
logs() {
    docker-compose logs -f ccblife-frontend
}

# 清理
cleanup() {
    docker-compose down --remove-orphans
    docker image prune -f
    log_info "清理完成"
}

# 主函数
case "${1:-deploy}" in
    deploy)
        check_docker
        create_dirs
        deploy
        ;;
    logs)
        logs
        ;;
    cleanup)
        cleanup
        ;;
    *)
        echo "用法: $0 {deploy|logs|cleanup}"
        exit 1
        ;;
esac 