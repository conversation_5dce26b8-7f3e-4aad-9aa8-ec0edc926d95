#!/bin/bash

# 前端启动脚本 - 自动配置网络别名

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查后端容器是否存在
check_backend() {
    if ! docker ps | grep -q "ccblife_server"; then
        log_error "后端容器 ccblife_server 未运行，请先启动后端服务"
        exit 1
    fi
    log_info "后端容器检查通过"
}

# 启动前端服务
start_frontend() {
    log_info "启动前端服务..."
    
    # 停止现有容器
    docker-compose down --remove-orphans
    
    # 构建并启动
    docker-compose up -d --build
    
    # 等待容器启动
    sleep 5
    
    # 为后端容器添加网络别名
    log_info "配置网络别名..."
    docker network connect ccblife_web_ccblife-network ccblife_server --alias ccblife-server 2>/dev/null || true
    
    log_info "前端服务启动完成"
    log_info "访问地址: http://localhost:8092"
}

# 主函数
main() {
    log_info "=== 前端服务启动 ==="
    check_backend
    start_frontend
    log_info "=== 启动完成 ==="
}

main "$@"
