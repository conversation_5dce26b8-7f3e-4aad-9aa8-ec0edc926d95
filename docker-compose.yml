version: '3.8'

name: ccblife_web

services:
  # 前端应用
  ccblife-frontend:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    image: ccblife_web:latest
    container_name: ccblife_web
    restart: unless-stopped
    ports:
      - "8092:8092"
    environment:
      - NODE_ENV=production
    volumes:
      - ./logs/nginx:/var/log/nginx
    networks:
      - ccblife-network

  # 后端API服务（如果有的话）
  ccblife-backend:
    image: ccblife-backend:latest
    container_name: ccblife-backend
    restart: unless-stopped
    ports:
      - "8090:8090"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=ccblife-mysql
      - DB_PORT=3306
      - DB_NAME=ccblife
      - DB_USERNAME=ccblife
      - DB_PASSWORD=ccblife123
    volumes:
      - ./logs/backend:/app/logs
    networks:
      - ccblife-network
    depends_on:
      - ccblife-mysql
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8090/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # MySQL数据库（如果需要）
  # ccblife-mysql:
  #   image: mysql:8.0
  #   container_name: ccblife-mysql
  #   restart: unless-stopped
  #   ports:
  #     - "3306:3306"
  #   environment:
  #     - MYSQL_ROOT_PASSWORD=root123
  #     - MYSQL_DATABASE=ccblife
  #     - MYSQL_USER=ccblife
  #     - MYSQL_PASSWORD=ccblife123
  #   volumes:
  #     - mysql_data:/var/lib/mysql
  #     - ./mysql/init:/docker-entrypoint-initdb.d
  #   networks:
  #     - ccblife-network
  #   command: --default-authentication-plugin=mysql_native_password
  #   healthcheck:
  #     test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 60s

  # Redis缓存（如果需要）
  # ccblife-redis:
  #   image: redis:7-alpine
  #   container_name: ccblife-redis
  #   restart: unless-stopped
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis_data:/data
  #   networks:
  #     - ccblife-network
  #   command: redis-server --appendonly yes
  #   healthcheck:
  #     test: ["CMD", "redis-cli", "ping"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 30s

networks:
  ccblife-network:
    driver: bridge

# volumes:
#   mysql_data:
#   redis_data: