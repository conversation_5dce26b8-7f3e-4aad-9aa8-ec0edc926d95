# 前端启动脚本 - 自动配置网络别名

Write-Host "=== 前端服务启动 ===" -ForegroundColor Green

# 检查后端容器是否存在
Write-Host "检查后端容器..." -ForegroundColor Yellow
$backendContainer = docker ps --filter "name=ccblife_server" --format "{{.Names}}"
if (-not $backendContainer) {
    Write-Host "错误: 后端容器 ccblife_server 未运行，请先启动后端服务" -ForegroundColor Red
    exit 1
}
Write-Host "后端容器检查通过" -ForegroundColor Green

# 启动前端服务
Write-Host "启动前端服务..." -ForegroundColor Yellow

# 停止现有容器
docker-compose down --remove-orphans

# 构建并启动
docker-compose up -d --build

# 等待容器启动
Start-Sleep -Seconds 5

# 为后端容器添加网络别名
Write-Host "配置网络别名..." -ForegroundColor Yellow
try {
    docker network connect ccblife_web_ccblife-network ccblife_server --alias ccblife-server 2>$null
} catch {
    # 忽略错误，可能已经连接
}

Write-Host "前端服务启动完成" -ForegroundColor Green
Write-Host "访问地址: http://localhost:8092" -ForegroundColor Cyan
Write-Host "=== 启动完成 ===" -ForegroundColor Green
