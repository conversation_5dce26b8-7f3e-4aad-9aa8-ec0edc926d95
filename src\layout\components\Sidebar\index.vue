<template>
  <div :class="{ 'has-logo': showLogo }" class="sidebar-container">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="getMenuBackground"
        :text-color="getMenuTextColor"
        :unique-opened="true"
        :active-text-color="theme"
        :collapse-transition="false"
        mode="vertical"
        :class="sideTheme"
      >
        <sidebar-item
          v-for="(route, index) in sidebarRouters"
          :key="route.path + index"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup>
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import variables from '@/assets/styles/variables.module.scss'
import useAppStore from '@/store/modules/app'
import useSettingsStore from '@/store/modules/settings'
import usePermissionStore from '@/store/modules/permission'

const route = useRoute()
const appStore = useAppStore()
const settingsStore = useSettingsStore()
const permissionStore = usePermissionStore()

const sidebarRouters = computed(() => permissionStore.sidebarRouters)
const showLogo = computed(() => settingsStore.sidebarLogo)
const sideTheme = computed(() => settingsStore.sideTheme)
const theme = computed(() => settingsStore.theme)
const isCollapse = computed(() => !appStore.sidebar.opened)

// 获取菜单背景色
const getMenuBackground = computed(() => {
  if (settingsStore.isDark) {
    return 'var(--sidebar-bg)'
  }
  return sideTheme.value === 'theme-dark' ? variables.menuBg : variables.menuLightBg
})

// 获取菜单文字颜色
const getMenuTextColor = computed(() => {
  if (settingsStore.isDark) {
    return 'var(--sidebar-text)'
  }
  return sideTheme.value === 'theme-dark' ? variables.menuText : variables.menuLightText
})

const activeMenu = computed(() => {
  const { meta, path } = route
  if (meta.activeMenu) {
    return meta.activeMenu
  }
  return path
})
</script>

<style lang="scss" scoped>
.sidebar-container {
  background: linear-gradient(180deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  box-shadow: 2px 0 20px rgba(0, 0, 0, 0.15);
  
  .scrollbar-wrapper {
    background: transparent;
  }

  .el-menu {
    border: none;
    height: 100%;
    width: 100% !important;
    background: transparent !important;
    
    .el-menu-item, .el-sub-menu__title {
      margin: 4px 8px;
      border-radius: 8px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      
      &:hover {
        background: linear-gradient(135deg, rgba(64, 158, 255, 0.1) 0%, rgba(64, 158, 255, 0.05) 100%) !important;
        transform: translateX(4px);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
      }
    }

    .el-menu-item {
      color: #e8eaed;
      font-weight: 500;
      position: relative;
      
      &.is-active {
        color: #ffffff;
        background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%) !important;
        box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);
        transform: translateX(4px);
        
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 3px;
          background: linear-gradient(180deg, #ffffff 0%, #e3f2fd 100%);
          border-radius: 0 2px 2px 0;
        }
      }
    }

    .el-sub-menu__title {
      color: #e8eaed;
      font-weight: 500;
      
      &:hover {
        color: #ffffff;
      }
    }
    
    .el-sub-menu {
      .el-menu-item {
        margin-left: 16px;
        margin-right: 16px;
        
        &.is-active {
          margin-left: 20px;
        }
      }
    }
  }
  
  // 滚动条样式
  .el-scrollbar__bar {
    &.is-vertical {
      .el-scrollbar__thumb {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 4px;
        
        &:hover {
          background: rgba(255, 255, 255, 0.3);
        }
      }
    }
  }
}
</style>
